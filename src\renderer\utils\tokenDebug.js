/**
 * Token调试工具
 * 用于检查和调试token同步问题
 */

/**
 * 获取当前渲染进程中的token
 */
export function getRendererToken() {
  try {
    const token = localStorage.getItem('userAuthToken') || ''
    console.log('🔍 [TOKEN_DEBUG] 渲染进程token:', token ? `${token.substring(0, 20)}...` : '无token')
    return token
  } catch (error) {
    console.error('🔍 [TOKEN_DEBUG] 获取渲染进程token失败:', error)
    return ''
  }
}

/**
 * 获取主进程中的token
 */
export async function getMainProcessToken() {
  try {
    if (window.electronAPI && window.electronAPI.invoke) {
      const token = await window.electronAPI.invoke('get-main-process-token')
      console.log('🔍 [TOKEN_DEBUG] 主进程token:', token ? `${token.substring(0, 20)}...` : '无token')
      return token
    } else {
      console.warn('🔍 [TOKEN_DEBUG] electronAPI不可用')
      return ''
    }
  } catch (error) {
    console.error('🔍 [TOKEN_DEBUG] 获取主进程token失败:', error)
    return ''
  }
}

/**
 * 比较主进程和渲染进程的token是否一致
 */
export async function compareTokens() {
  const rendererToken = getRendererToken()
  const mainToken = await getMainProcessToken()
  
  const isConsistent = rendererToken === mainToken
  
  console.log('🔍 [TOKEN_DEBUG] Token一致性检查:', {
    rendererToken: rendererToken ? `${rendererToken.substring(0, 20)}...` : '无token',
    mainToken: mainToken ? `${mainToken.substring(0, 20)}...` : '无token',
    isConsistent,
    rendererLength: rendererToken.length,
    mainLength: mainToken.length
  })
  
  if (!isConsistent) {
    console.warn('⚠️ [TOKEN_DEBUG] Token不一致！需要同步')
    return {
      consistent: false,
      rendererToken,
      mainToken,
      issue: 'tokens_mismatch'
    }
  }
  
  if (!rendererToken && !mainToken) {
    console.warn('⚠️ [TOKEN_DEBUG] 两个进程都没有token')
    return {
      consistent: true,
      rendererToken,
      mainToken,
      issue: 'no_tokens'
    }
  }
  
  console.log('✅ [TOKEN_DEBUG] Token一致性检查通过')
  return {
    consistent: true,
    rendererToken,
    mainToken,
    issue: null
  }
}

/**
 * 强制同步token到主进程
 */
export async function syncTokenToMainProcess() {
  try {
    const rendererToken = getRendererToken()
    if (!rendererToken) {
      console.warn('🔍 [TOKEN_DEBUG] 渲染进程没有token，无法同步')
      return false
    }
    
    if (window.electronAPI && window.electronAPI.invoke) {
      const result = await window.electronAPI.invoke('sync-token-to-main', rendererToken)
      console.log('🔍 [TOKEN_DEBUG] 同步token到主进程结果:', result)
      return result.success
    } else {
      console.warn('🔍 [TOKEN_DEBUG] electronAPI不可用，无法同步')
      return false
    }
  } catch (error) {
    console.error('🔍 [TOKEN_DEBUG] 同步token到主进程失败:', error)
    return false
  }
}

/**
 * 完整的token诊断
 */
export async function diagnoseTokens() {
  console.log('🔍 [TOKEN_DEBUG] 开始token诊断...')
  
  const comparison = await compareTokens()
  
  if (!comparison.consistent) {
    console.log('🔍 [TOKEN_DEBUG] 尝试自动修复token不一致问题...')
    const syncResult = await syncTokenToMainProcess()
    
    if (syncResult) {
      console.log('🔍 [TOKEN_DEBUG] 自动修复成功，重新检查...')
      const recheck = await compareTokens()
      return recheck
    } else {
      console.error('🔍 [TOKEN_DEBUG] 自动修复失败')
      return comparison
    }
  }
  
  return comparison
}

/**
 * 在控制台暴露调试工具
 */
if (typeof window !== 'undefined') {
  window.tokenDebug = {
    getRenderer: getRendererToken,
    getMain: getMainProcessToken,
    compare: compareTokens,
    sync: syncTokenToMainProcess,
    diagnose: diagnoseTokens
  }
  
  console.log('🔍 [TOKEN_DEBUG] Token调试工具已加载，使用 window.tokenDebug 访问')
}
