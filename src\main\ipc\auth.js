const { ipcMain, shell, app } = require('electron')
const Store = require('electron-store')

/**
 * 认证相关的IPC处理程序
 */
class AuthIPCHandler {
  constructor(appManager) {
    this.appManager = appManager
    this.store = new Store()
  }

  /**
   * 注册所有认证相关的IPC处理程序
   */
  register() {
    // 处理用户登录事件
    ipcMain.handle('login', async (event, credentials) => {
      console.log('收到登录请求', credentials.username)
      // 在这里处理登录逻辑
      if (credentials.username && credentials.password) {
        this.appManager.isLoggedIn = true
        this.store.set('userAuthenticatedStatus', true)
        
        // 通知悬浮窗更新状态
        if (this.appManager.windowManager.isFloatingWindowValid()) {
          this.appManager.windowManager.getFloatingWindow().webContents.send('login-status-changed', true)
        }
        
        return { success: true, message: '登录成功' }
      } else {
        return { success: false, message: '用户名或密码不能为空' }
      }
    })

    // 处理用户注销事件
    ipcMain.handle('logout', async (event) => {
      console.log('🚪 收到注销请求')
      this.appManager.isLoggedIn = false
      
      // 彻底清除所有认证相关的存储
      this.store.delete('userAuthenticatedStatus')
      this.store.delete('userAuthInfo')
      this.store.delete('userAuthToken')
      this.store.delete('userInfo')
      this.store.delete('authToken')
      this.store.delete('refreshToken')
      this.store.delete('tokenExpiry')
      console.log('🔑 已清除主进程中的所有认证信息')
      
      // 通知悬浮窗更新状态
      if (this.appManager.windowManager.isFloatingWindowValid()) {
        this.appManager.windowManager.getFloatingWindow().webContents.send('login-status-changed', false)
      }

      // 退出登录时关闭悬浮窗
      if (this.appManager.windowManager.isFloatingWindowValid()) {
        console.log('🔄 退出登录，关闭悬浮窗')
        this.appManager.windowManager.getFloatingWindow().close()
      }

      return { success: true, message: '注销成功' }
    })

    // 获取登录状态
    ipcMain.handle('get-login-status', () => {
      const status = this.store.get('userAuthenticatedStatus', false)
      console.log('Getting login status:', status)
      return status
    })

    // 处理前端登录状态通知
    ipcMain.handle('set-login-status', async (event, loginData) => {
      console.log('🔄 收到前端登录状态通知 (AppManager):', loginData)

      try {
        if (loginData.isLoggedIn) {
          // 登录成功
          this.appManager.isLoggedIn = true
          this.store.set('userAuthenticatedStatus', true)
          
          // 保存用户信息
          if (loginData.user) {
            this.store.set('userAuthInfo', loginData.user)
          }

          // 保存用户Token（确保主进程和渲染进程token同步）
          if (loginData.token) {
            this.store.set('userAuthToken', loginData.token)
            console.log('🔑 已保存用户Token到主进程存储:', loginData.token.substring(0, 20) + '...')
          }

          console.log('🔄 用户登录成功，开始初始化服务...')
          
          // 初始化服务
          await this.appManager.initializeServices()

          // 创建悬浮窗口
          this.appManager.windowManager.createFloatingWindow()
        } else {
          // 登录失败或注销
          this.appManager.isLoggedIn = false
          this.store.delete('userAuthenticatedStatus')
          this.store.delete('userAuthInfo')
          this.store.delete('userAuthToken')
          this.store.delete('userInfo')
          console.log('🔑 已清除主进程中的认证信息')

          // 退出登录时关闭悬浮窗
          if (this.appManager.windowManager.isFloatingWindowValid()) {
            console.log('🔄 退出登录，关闭悬浮窗')
            this.appManager.windowManager.getFloatingWindow().close()
          }
        }

        // 通知悬浮窗更新状态
        if (this.appManager.windowManager.isFloatingWindowValid()) {
          this.appManager.windowManager.getFloatingWindow().webContents.send('login-status-changed', loginData.isLoggedIn)
          console.log('🔄 已通知悬浮窗登录状态变化:', loginData.isLoggedIn)
        }

        // 更新托盘菜单状态
        this.appManager.windowManager.updateTrayMenu()

        return { success: true }
      } catch (error) {
        console.error('处理登录状态变化失败:', error)
        return { success: false, error: error.message }
      }
    })

    // Token调试相关IPC处理程序
    ipcMain.handle('get-main-process-token', async () => {
      try {
        const token = this.store.get('userAuthToken', '')
        console.log('🔍 [TOKEN_DEBUG] 主进程token查询:', token ? `${token.substring(0, 20)}...` : '无token')
        return token
      } catch (error) {
        console.error('🔍 [TOKEN_DEBUG] 获取主进程token失败:', error)
        return ''
      }
    })

    ipcMain.handle('sync-token-to-main', async (event, token) => {
      try {
        if (token) {
          this.store.set('userAuthToken', token)
          console.log('🔍 [TOKEN_DEBUG] 已同步token到主进程:', token.substring(0, 20) + '...')
          return { success: true, message: 'Token同步成功' }
        } else {
          console.warn('🔍 [TOKEN_DEBUG] 尝试同步空token')
          return { success: false, message: 'Token为空' }
        }
      } catch (error) {
        console.error('🔍 [TOKEN_DEBUG] 同步token失败:', error)
        return { success: false, message: error.message }
      }
    })

    console.log('✅ 认证相关IPC处理程序已注册')
  }
}

module.exports = AuthIPCHandler
