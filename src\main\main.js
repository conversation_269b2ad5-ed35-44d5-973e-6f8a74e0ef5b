const { app, BrowserWindow, screen, Tray, Menu, nativeImage, shell } = require('electron')
const { join, basename } = require('path')
const fs = require('fs')
const Store = require('electron-store')
let { spawn } = require('child_process')
const os = require('os')
const path = require('path')

// 引入 API 客户端模块
const { createMainApiClient, handleMainApiError, callMainAIService, MAIN_API_CONFIG } = require('./apiClient')

// 引入知识库模块
const knowledge = require('./knowledge')

// 引入 MCP 客户端管理器
const MCPClientManager = require('./mcp/clientManager')
const EmailService = require('./services/emailService')
const OutlookCalendarService = require('./services/outlookCalendarService')

// 引入IPC管理器
const IPCManager = require('./ipc')

// === Console 转发设置 ===
let globalAppManager = null

// 保存原始console方法
const originalConsole = {
  log: console.log,
  warn: console.warn,
  error: console.error,
  info: console.info,
  debug: console.debug
}

// 重写console方法，转发到浏览器控制台
function setupConsoleForwarding() {
  const forwardConsole = (level, originalMethod) => {
    return (...args) => {
      // 调用原始方法，保持终端输出
      originalMethod.apply(console, args)

      // 转发到浏览器控制台
      if (globalAppManager && globalAppManager.windowManager && globalAppManager.windowManager.isMainWindowValid()) {
        try {
          const mainWindow = globalAppManager.windowManager.getMainWindow()
          if (mainWindow && !mainWindow.isDestroyed()) {
            // 将参数序列化为字符串
            const message = args.map(arg => {
              if (typeof arg === 'object') {
                try {
                  return JSON.stringify(arg, null, 2)
                } catch (e) {
                  return String(arg)
                }
              }
              return String(arg)
            }).join(' ')

            mainWindow.webContents.send('main-console-log', {
              level,
              message,
              timestamp: new Date().toISOString()
            })
          }
        } catch (error) {
          // 避免转发过程中出现循环错误
          originalConsole.log('Console转发失败:', error.message)
        }
      }
    }
  }

  console.log = forwardConsole('log', originalConsole.log)
  console.warn = forwardConsole('warn', originalConsole.warn)
  console.error = forwardConsole('error', originalConsole.error)
  console.info = forwardConsole('info', originalConsole.info)
  console.debug = forwardConsole('debug', originalConsole.debug)
}

/**
 * 获取当前用户token（保留在主进程中供其他模块使用）
 */
async function getCurrentUserToken() {
  let userToken = ''

  // 优先从渲染进程获取最新的token（确保获取到最新的token）
  if (global.mainWindow && global.mainWindow.webContents) {
    try {
      userToken = await global.mainWindow.webContents.executeJavaScript(`
        localStorage.getItem('userAuthToken') || ''
      `);
      if (userToken) {
        console.log('🔑 从渲染进程获取到最新token:', userToken.substring(0, 20) + '...')
        // 同步到主进程存储
        try {
          const Store = require('electron-store')
          const store = new Store()
          const storedToken = store.get('userAuthToken', '')
          // 只有当token不同时才更新
          if (storedToken !== userToken) {
            store.set('userAuthToken', userToken)
            console.log('🔑 已同步最新token到主进程存储')
          }
        } catch (error) {
          console.log('⚠️ 同步token到主进程存储失败:', error.message);
        }
        return userToken
      }
    } catch (error) {
      console.log('⚠️ 无法从渲染进程获取用户token:', error.message);
    }
  }

  // 如果渲染进程获取失败，再尝试从主进程存储中获取
  try {
    const Store = require('electron-store')
    const store = new Store()
    userToken = store.get('userAuthToken', '')
    if (userToken) {
      console.log('🔑 从主进程存储获取到token:', userToken.substring(0, 20) + '...')
      return userToken
    }
  } catch (error) {
    console.log('⚠️ 从主进程存储获取token失败:', error.message);
  }

  console.log('⚠️ 未找到用户token，需要用户登录')
  return userToken
}


// === 知识库服务定义结束 ===

// 在Windows上全局隐藏子进程窗口 - 彻底拦截所有spawn调用
if (process.platform === 'win32') {
  process.env.PYTHONUNBUFFERED = '1'
  process.env.PYTHONIOENCODING = 'utf-8'

  // 彻底覆盖child_process模块的spawn方法
  const childProcess = require('child_process')
  const originalSpawn = childProcess.spawn

  // 覆盖spawn方法 - 确保所有调用都被拦截
  childProcess.spawn = function (...args) {
    console.log('🔧 拦截spawn调用:', args[0], args[1]?.slice(0, 2) || [])

    // 检查是否是Outlook日历MCP服务器进程（只针对Node.js的本地MCP服务器）
    const isOutlookMCPServer = args[0] === 'node' && args[1] && args[1].some(arg =>
      arg && arg.includes('outlook-calendar-local.js')
    )

    if (isOutlookMCPServer) {
      console.log('🔧 检测到Outlook日历MCP服务器进程，完全跳过拦截')
      // 对于Outlook日历MCP服务器，完全不修改任何参数，直接调用原始spawn
      return originalSpawn.apply(this, args)
    } else {
      // 对于其他进程（包括Python MCP服务器），使用原来的强制pipe模式
      if (args[2] && typeof args[2] === 'object') {
        // 确保所有必要的窗口隐藏选项都被设置
        args[2].windowsHide = true
        args[2].shell = false
        args[2].detached = false
        args[2].windowsVerbatimArguments = false
        // 强制使用pipe模式，不继承任何输出流
        args[2].stdio = ['pipe', 'pipe', 'pipe']
        args[2].flags = 0x08000000 // CREATE_NO_WINDOW flag
      } else if (!args[2]) {
        args[2] = {
          windowsHide: true,
          shell: false,
          detached: false,
          windowsVerbatimArguments: false,
          stdio: ['pipe', 'pipe', 'pipe'],
          flags: 0x08000000 // CREATE_NO_WINDOW flag
        }
      }
    }

    console.log('🔧 spawn配置 (强制pipe模式):', {
      windowsHide: args[2].windowsHide,
      stdio: args[2].stdio,
      flags: args[2].flags,
      shell: args[2].shell
    })
    return originalSpawn.apply(this, args)
  }

  // 同时覆盖模块缓存中的spawn - 确保SDK也使用我们的版本
  const Module = require('module')
  const originalRequire = Module.prototype.require
  Module.prototype.require = function (id) {
    const module = originalRequire.apply(this, arguments)
    if (id === 'child_process' && module.spawn !== childProcess.spawn) {
      console.log('🔧 修复child_process模块的spawn方法')
      module.spawn = childProcess.spawn
    }
    return module
  }
}

// MCP SDK将通过动态导入加载
let StdioClientTransport, Client, ListToolsRequestSchema, CallToolRequestSchema

const store = new Store()
const isDev = !app.isPackaged
const isDebugMode = process.env.ELECTRON_DEBUG === 'true'

console.log('Electron main process starting...')
console.log('isDev:', isDev)
console.log('isDebugMode:', isDebugMode)
console.log('__dirname:', __dirname)

class AppManager {
  constructor() {
    this.isLoggedIn = false
    this.hasShownTrayNotification = false
    this.mcpManager = new MCPClientManager()
    this.emailService = null
    this.outlookCalendarService = null
    this.servicesInitialized = false
    this.displayScale = 1.0 // 保存Windows缩放比例
    this.app = app // 保存app引用

    // 初始化窗口管理器
    const WindowManager = require('./window/windowManager')
    this.windowManager = new WindowManager(this)

    // 初始化IPC管理器
    this.ipcManager = new IPCManager(this)
  }

  // 获取Windows桌面缩放比例
  getWindowsScaleFactor() {
    try {
      const primaryDisplay = screen.getPrimaryDisplay()
      const scaleFactor = primaryDisplay.scaleFactor
      console.log('=== 缩放比例检测调试信息 ===')
      console.log('系统原始scaleFactor:', scaleFactor)

      let mappedScale
      // 只支持标准缩放级别（100%, 125%, 150%, 175%），其他都按100%处理
      if (scaleFactor >= 1.24 && scaleFactor <= 1.26) {
        mappedScale = 1.25 // 精确匹配125%
        console.log('检测为 125% 缩放')
      } else if (scaleFactor >= 1.49 && scaleFactor <= 1.51) {
        mappedScale = 1.5 // 精确匹配150%
        console.log('检测为 150% 缩放')
      } else if (scaleFactor >= 1.74 && scaleFactor <= 1.76) {
        mappedScale = 1.75 // 精确匹配175%
        console.log('检测为 175% 缩放')
      } else {
        mappedScale = 1.0 // 其他情况都按100%处理
        console.log(`系统缩放${scaleFactor * 100}%不在支持范围内，按 100% 处理`)
      }

      console.log(`最终返回的缩放比例: ${mappedScale}`)
      return mappedScale
    } catch (error) {
      console.error('获取缩放比例失败:', error)
      return 1.0 // 默认100%
    }
  }

  // 应用窗口缩放（委托给窗口管理器）
  applyWindowScale(window) {
    try {
      const reverseScale = 1.0 / this.displayScale
      console.log(`应用反向缩放: ${reverseScale} (原始缩放: ${this.displayScale})`)

      // 通过CSS缩放来抵消Windows的缩放效果
      window.webContents.setZoomFactor(reverseScale)

      console.log(`✅ 窗口缩放已应用: ${reverseScale}`)
    } catch (error) {
      console.error('❌ 应用窗口缩放失败:', error)
    }
  }

  setupSessionPermissions() {
    // 配置session权限以支持各种AI服务
    const { session } = require('electron')

    // 允许麦克风访问权限
    session.defaultSession.setPermissionRequestHandler((webContents, permission, callback) => {
      console.log('Permission request:', permission)

      // 允许麦克风和摄像头权限（用于语音识别）
      if (permission === 'microphone' || permission === 'camera' || permission === 'media') {
        console.log('Granting media permission for ASR')
        callback(true)
        return
      }

      // 允许通知权限
      if (permission === 'notifications') {
        callback(true)
        return
      }

      // 其他权限默认拒绝
      console.log('Denying permission:', permission)
      callback(false)
    })

    // 配置CSP以允许各种AI服务
    session.defaultSession.webRequest.onHeadersReceived((details, callback) => {
      const responseHeaders = details.responseHeaders || {}

      // 添加CORS头以允许各种AI服务API
      responseHeaders['Access-Control-Allow-Origin'] = ['*']
      responseHeaders['Access-Control-Allow-Methods'] = ['GET, POST, PUT, DELETE, OPTIONS']
      responseHeaders['Access-Control-Allow-Headers'] = ['Content-Type, Authorization, X-Requested-With']

      callback({ responseHeaders })
    })

    // 允许访问各种AI服务的API
    session.defaultSession.webRequest.onBeforeRequest((details, callback) => {
      // 允许访问常用AI服务API
      if (details.url.includes('api.siliconflow.cn') ||
        details.url.includes('asr.cloud.tencent.com') ||
        details.url.includes('tencentcloudapi.com') ||
        details.url.includes('api.openai.com') ||
        details.url.includes('api.anthropic.com') ||
        details.url.includes('*************:9603') ||
        details.url.includes('114.80.40.197:31080') ||
        details.url.includes('*************:2345')) {
        console.log('Allowing AI service request:', details.url)
        callback({})
        return
      }
      callback({})
    })

    // 配置CSP以允许WebSocket连接
    session.defaultSession.webRequest.onHeadersReceived((details, callback) => {
      const responseHeaders = details.responseHeaders || {}

      // 如果响应头中有CSP，则修改它以允许WebSocket连接
      if (responseHeaders['content-security-policy']) {
        const csp = responseHeaders['content-security-policy'][0]

        // 检查是否已经包含WebSocket协议
        if (!csp.includes('ws://*************:9603')) {
          // 添加WebSocket协议到connect-src
          const newCsp = csp.replace(
            'connect-src \'self\'',
            'connect-src \'self\' ws://*************:9603 wss://*************:9603'
          )
          responseHeaders['content-security-policy'] = [newCsp]
          console.log('Updated CSP to allow WebSocket connections')
        }
      }

      callback({ responseHeaders })
    })

    console.log('Session permissions configured for AI services')
  }

  setupIPC() {
    console.log('🔧 设置IPC处理器...')

    // 使用IPC管理器注册所有IPC处理程序
    this.ipcManager.registerAll()

    console.log('✅ 所有IPC处理程序已通过IPC管理器注册完成')
  }



  async init() {
    console.log('Initializing app manager...')
    setupConsoleForwarding()
    // 获取Windows缩放比例
    this.displayScale = this.getWindowsScaleFactor()
    console.log(`应用启动时检测到Windows缩放比例: ${this.displayScale * 100}%`)

    // 初始化窗口管理器
    this.windowManager.initialize()

    // 监听屏幕变化（例如用户更改缩放比例）
    screen.on('display-metrics-changed', () => {
      console.log('检测到显示器配置变化，重新获取缩放比例...')
      const newScale = this.getWindowsScaleFactor()
      if (newScale !== this.displayScale) {
        console.log(`Windows缩放比例从 ${this.displayScale * 100}% 更改为 ${newScale * 100}%`)
        this.displayScale = newScale

        // 重新应用反向缩放到已存在的窗口
        this.windowManager.repositionAllWindows()
      }
    })

    this.setupSessionPermissions() // 配置session权限，支持腾讯云ASR
    this.setupIPC()

    // 发送初始状态消息
    if (this.windowManager.isMainWindowValid()) {
      this.windowManager.getMainWindow().webContents.send('status-message', '正在初始化应用...')
    }

    // 新的 Sherpa-ONNX 集成方式：使用 Web Audio API 和 WASM，无需主进程处理
    console.log('新的 Sherpa-ONNX 集成方式：使用 Web Audio API 和 WASM，无需主进程处理')

    // 不自动恢复登录状态，让渲染进程通过token验证来决定
    this.isLoggedIn = false
    console.log('🔄 主进程不自动恢复登录状态，等待渲染进程验证')

    console.log('⏳ 等待用户登录后再初始化服务...')
    if (this.windowManager.isMainWindowValid()) {
      this.windowManager.getMainWindow().webContents.send('status-message', '请先登录以启动应用')
    }
  }


  async initializeServices() {
    console.log('🚀 开始初始化服务...')

    // 如果服务已经初始化，只重新初始化知识库
    if (this.servicesInitialized) {
      console.log('🔄 服务已经初始化，重新初始化知识库...')
      try {
        // 设置知识库模块的getCurrentUserToken函数
        knowledge.setGetCurrentUserTokenFunction(getCurrentUserToken)
        // 重新初始化知识库
        const initSuccess = await knowledge.initializeKnowledge()
        if (initSuccess) {
          console.log('✅ 知识库模块重新初始化完成')
        } else {
          console.error('❌ 知识库模块重新初始化失败')
        }
        return
      } catch (error) {
        console.error('❌ 知识库模块重新初始化失败:', error)
        return
      }
    }

    // 标记服务正在初始化，防止重复调用
    this.servicesInitialized = true

    // 初始化知识库模块
    try {
      console.log('🧠 初始化知识库模块...')
      // 设置知识库模块的getCurrentUserToken函数
      knowledge.setGetCurrentUserTokenFunction(getCurrentUserToken)
      // 初始化知识库
      const initSuccess = await knowledge.initializeKnowledge()
      if (!initSuccess) {
        throw new Error('知识库初始化返回失败')
      }
      console.log('✅ 知识库模块初始化完成')
    } catch (error) {
      console.error('❌ 知识库模块初始化失败:', error)
      console.error('❌ 尝试重新初始化知识库...')

      // 尝试重新初始化
      try {
        console.log('🔄 重新初始化知识库模块...')
        const retrySuccess = await knowledge.initializeKnowledge()
        if (retrySuccess) {
          console.log('✅ 知识库模块重新初始化成功')
        } else {
          console.error('❌ 知识库模块重新初始化失败，将影响知识库功能')
        }
      } catch (retryError) {
        console.error('❌ 知识库模块重新初始化失败:', retryError)
        console.error('❌ 知识库功能将不可用')
      }
    }

    // 等待主窗口完全加载
    if (this.windowManager.isMainWindowValid()) {
      const mainWindow = this.windowManager.getMainWindow()
      // 确保主窗口已加载完成
      if (mainWindow.webContents.isLoading()) {
        console.log('⏳ 等待主窗口加载完成...')
        await new Promise((resolve) => {
          mainWindow.webContents.once('did-finish-load', resolve)
        })
      }
      mainWindow.webContents.send('status-message', '正在初始化浏览器控制，请稍候...')
    }

    // 初始化MCP管理器
    try {
      await this.mcpManager.initialize()
      console.log('MCP Manager initialized successfully')

      if (this.windowManager.isMainWindowValid()) {
        this.windowManager.getMainWindow().webContents.send('status-message', '正在初始化邮件服务...')
      }

      // 初始化邮件服务（不立即检查邮件）
      try {
        console.log('🔧 开始创建EmailService实例...')
        this.emailService = new EmailService(this.mcpManager, this.windowManager.getMainWindow())
        console.log('🔧 EmailService实例创建成功，开始初始化...')
        await this.emailService.initialize()
        console.log('✅ Email Service initialized successfully')

        // 检查是否有邮件配置
        const store = new Store()
        const emailConfig = store.get('emailConfig')

        if (emailConfig && emailConfig.user && emailConfig.pass) {
          if (this.windowManager.isMainWindowValid()) {
            this.windowManager.getMainWindow().webContents.send('status-message', '邮件服务初始化完成')
          }
        } else {
          if (this.windowManager.isMainWindowValid()) {
            this.windowManager.getMainWindow().webContents.send('status-message', '邮件服务已初始化（未配置邮箱）')
          }
        }
      } catch (error) {
        console.error('❌ Failed to initialize Email Service:', error)
        if (this.windowManager.isMainWindowValid()) {
          this.windowManager.getMainWindow().webContents.send('status-message', '邮件服务初始化失败，将以有限功能运行')
        }
      }

      if (this.windowManager.isMainWindowValid()) {
        this.windowManager.getMainWindow().webContents.send('status-message', '正在初始化日历服务...')
      }

      // 初始化Outlook日历服务
      try {
        this.outlookCalendarService = new OutlookCalendarService(this.mcpManager, this.windowManager.getMainWindow())
        await this.outlookCalendarService.initialize()
        console.log('Outlook Calendar Service initialized successfully')
        if (this.windowManager.isMainWindowValid()) {
          this.windowManager.getMainWindow().webContents.send('status-message', '日历服务初始化完成')
        }
      } catch (error) {
        console.error('Failed to initialize Outlook Calendar Service:', error)
        if (this.windowManager.isMainWindowValid()) {
          this.windowManager.getMainWindow().webContents.send('status-message', '日历服务初始化失败，将以有限功能运行')
        }
      }

      // 所有MCP服务初始化完成后，检查是否需要开始首次邮件检查
      const store = new Store()
      const emailConfig = store.get('emailConfig')

      if (emailConfig && emailConfig.user && emailConfig.pass) {
        console.log('🚀 所有MCP服务初始化完成，开始首次邮件检查...')
        if (this.windowManager.isMainWindowValid()) {
          this.windowManager.getMainWindow().webContents.send('status-message', '正在检查邮件...')
        }

        try {
          await this.emailService?.startInitialEmailCheck()
          if (this.windowManager.isMainWindowValid()) {
            this.windowManager.getMainWindow().webContents.send('status-message', '邮件检查完成')
          }
        } catch (error) {
          console.error('Failed to check emails:', error)
          if (this.windowManager.isMainWindowValid()) {
            this.windowManager.getMainWindow().webContents.send('status-message', '邮件检查失败，稍后将重试')
          }
        }
      } else {
        console.log('🚀 所有MCP服务初始化完成（未配置邮箱，跳过邮件检查）')
        if (this.windowManager.isMainWindowValid()) {
          this.windowManager.getMainWindow().webContents.send('status-message', '应用初始化完成（未配置邮箱）')
        }
      }

      this.servicesInitialized = true
      console.log('✅ 所有服务初始化完成')

    } catch (error) {
      console.error('Failed to initialize MCP Manager:', error)
      if (this.windowManager.isMainWindowValid()) {
        this.windowManager.getMainWindow().webContents.send('status-message', 'MCP服务初始化部分失败，应用将以有限功能运行')
      }
    }

    // 初始化完成后发送状态更新
    if (this.windowManager.isMainWindowValid()) {
      this.windowManager.getMainWindow().webContents.send('status-message', '应用初始化完成')
      console.log('✅ 已发送应用初始化完成消息到主窗口')
      console.log('🔍 浏览器客户端状态:', this.mcpManager.clients.has('browser') ? '已初始化' : '未初始化')
    } else {
      console.warn('⚠️ 主窗口不可用，无法发送应用初始化完成消息')
    }

    // 同时发送到悬浮窗（如果存在）
    if (this.windowManager.isFloatingWindowValid()) {
      this.windowManager.getFloatingWindow().webContents.send('status-message', '应用初始化完成')
      console.log('✅ 已发送应用初始化完成消息到悬浮窗')
    }

    // 🔄 【新增】等待主窗口加载完成后再发送状态消息，确保消息能被正确接收
    if (this.windowManager.isMainWindowValid()) {
      const mainWindow = this.windowManager.getMainWindow()
      // 确保主窗口已完全加载
      if (mainWindow.webContents.isLoading()) {
        console.log('⏳ 等待主窗口完全加载后再发送状态消息...')
        await new Promise((resolve) => {
          mainWindow.webContents.once('did-finish-load', resolve)
        })
      }

      // 再次发送应用初始化完成消息，确保主窗口能接收到
      mainWindow.webContents.send('status-message', '应用初始化完成')
      console.log('✅ 主窗口加载完成后再次发送应用初始化完成消息')
    }
  }
}

const appManager = new AppManager()
globalAppManager = appManager

// === 协议注册 ===
// 设置为协议的默认处理器
if (process.defaultApp) {
  if (process.argv.length >= 2) {
    app.setAsDefaultProtocolClient('ai-cognidesk', process.execPath, [path.resolve(process.argv[1])])
  }
} else {
  app.setAsDefaultProtocolClient('ai-cognidesk')
}

console.log('🔗 协议处理器已注册: ai-cognidesk://')

// 在开发环境中，强制注册协议处理器
if (isDev) {
  console.log('🔗 开发环境：强制注册协议处理器')
  const { spawn } = require('child_process')

  // Windows: 通过注册表注册协议
  if (process.platform === 'win32') {
    const appPath = process.execPath
    const registryScript = `
      Windows Registry Editor Version 5.00

      [HKEY_CURRENT_USER\\Software\\Classes\\ai-cognidesk]
      @="URL:ai-cognidesk Protocol"
      "URL Protocol"=""

      [HKEY_CURRENT_USER\\Software\\Classes\\ai-cognidesk\\DefaultIcon]
      @="${appPath.replace(/\\/g, '\\\\')},1"

      [HKEY_CURRENT_USER\\Software\\Classes\\ai-cognidesk\\shell]

      [HKEY_CURRENT_USER\\Software\\Classes\\ai-cognidesk\\shell\\open]

      [HKEY_CURRENT_USER\\Software\\Classes\\ai-cognidesk\\shell\\open\\command]
      @="\\"${appPath.replace(/\\/g, '\\\\')}\\" \\"%1\\""
    `

    const fs = require('fs')
    const tempRegFile = path.join(os.tmpdir(), 'ai-cognidesk-protocol.reg')

    try {
      fs.writeFileSync(tempRegFile, registryScript)
      console.log('🔗 创建注册表文件:', tempRegFile)

      // 导入注册表
      const regProcess = spawn('reg', ['import', tempRegFile], {
        shell: true,
        stdio: 'inherit'
      })

      regProcess.on('close', (code) => {
        console.log('🔗 注册表导入完成，退出码:', code)
        // 清理临时文件
        try {
          fs.unlinkSync(tempRegFile)
        } catch (e) {
          console.warn('🔗 清理临时文件失败:', e.message)
        }
      })

      regProcess.on('error', (error) => {
        console.error('🔗 注册表导入失败:', error)
      })
    } catch (error) {
      console.error('🔗 创建注册表文件失败:', error)
    }
  }
}

// === 单实例应用检查 ===
// 获取单实例锁，防止多开
const gotTheLock = app.requestSingleInstanceLock()

// 🔄 防重复发送SSO回调的标记
let lastSentSSOCode = null
let lastSentTime = 0

if (!gotTheLock) {
  // 如果无法获取锁，说明已经有一个实例在运行
  console.log('🔒 应用已经在运行，退出当前实例')
  app.quit()
} else {
  // 监听第二个实例尝试启动的事件
  app.on('second-instance', (event, commandLine, workingDirectory) => {
    console.log('🔒 检测到第二个实例尝试启动，激活现有窗口')
    console.log('🔒 命令行参数:', commandLine)

    // Windows: 处理 ai-cognidesk:// 协议链接
    const protocolArg = commandLine.find(arg => arg.startsWith('ai-cognidesk://auth/sso/callback'))
    if (protocolArg) {
      console.log('🔗 收到 ai-cognidesk 协议链接:', protocolArg)
      try {
        const url = new URL(protocolArg)
        const auth_code = url.searchParams.get('code')
        console.log('🔗 提取的授权码:', auth_code)

        if (auth_code) {
          // 🔄 防重复发送：检查是否最近发送过相同的code
          const now = Date.now()
          if (lastSentSSOCode === auth_code && (now - lastSentTime) < 5000) {
            console.log('🔗 最近已发送过相同的授权码，跳过重复发送:', auth_code)
            return
          }

          // 发送授权码到渲染进程
          if (appManager.windowManager.isMainWindowValid()) {
            console.log('🔗 向主窗口发送授权码')
            appManager.windowManager.getMainWindow().webContents.send('sso-callback', { code: auth_code })

            // 更新发送记录
            lastSentSSOCode = auth_code
            lastSentTime = now
          }

          // 显示并激活主窗口
          if (appManager.windowManager.isMainWindowValid()) {
            const mainWindow = appManager.windowManager.getMainWindow()
            if (mainWindow.isMinimized()) {
              mainWindow.restore()
            }
            mainWindow.focus()
            mainWindow.show()
          }
        }
      } catch (error) {
        console.error('🔗 处理协议链接失败:', error)
      }
    } else {
      // 如果主窗口存在，激活它
      if (appManager.windowManager.isMainWindowValid()) {
        const mainWindow = appManager.windowManager.getMainWindow()
        if (mainWindow.isMinimized()) {
          mainWindow.restore()
        }
        mainWindow.focus()
        mainWindow.show()
      }

      // 如果悬浮窗口存在且已登录，也可以激活它
      if (appManager.windowManager.isFloatingWindowValid() && appManager.isLoggedIn) {
        appManager.windowManager.getFloatingWindow().show()
      }

      // 显示提示消息
      if (appManager.windowManager.isMainWindowValid()) {
        appManager.windowManager.getMainWindow().webContents.send('show-notification', {
          type: 'info',
          title: '应用已运行',
          message: '应用已经在运行中，无需重复启动。'
        })
      }
    }
  })

  // 应用准备就绪后初始化
  app.whenReady().then(async () => {
    console.log('Electron app ready')
    await appManager.init()

    // 处理首次启动时的协议参数
    const protocolArg = process.argv.find(arg => arg.startsWith('ai-cognidesk://auth/sso/callback'))
    if (protocolArg) {
      console.log('🔗 应用启动时收到协议链接:', protocolArg)
      setTimeout(() => {
        try {
          const url = new URL(protocolArg)
          const auth_code = url.searchParams.get('code')
          console.log('🔗 提取的授权码:', auth_code)

          if (auth_code && appManager.windowManager.isMainWindowValid()) {
            // 🔄 防重复发送：检查是否最近发送过相同的code
            const now = Date.now()
            if (lastSentSSOCode === auth_code && (now - lastSentTime) < 5000) {
              console.log('🔗 最近已发送过相同的授权码，跳过重复发送:', auth_code)
              return
            }

            console.log('🔗 向主窗口发送授权码')
            appManager.windowManager.getMainWindow().webContents.send('sso-callback', { code: auth_code })

            // 更新发送记录
            lastSentSSOCode = auth_code
            lastSentTime = now
          }
        } catch (error) {
          console.error('🔗 处理启动协议链接失败:', error)
        }
      }, 2000) // 等待2秒确保窗口已创建
    }

    app.on('activate', () => {
      if (BrowserWindow.getAllWindows().length === 0) {
        appManager.windowManager.showMainWindow()
      }
    })
  })
}

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit()
  }
})

// macOS 协议处理
app.on('open-url', (event, url) => {
  event.preventDefault()
  console.log('🔗 macOS 协议处理:', url)

  if (url.startsWith('ai-cognidesk://auth/sso/callback')) {
    try {
      const urlObj = new URL(url)
      const auth_code = urlObj.searchParams.get('code')
      console.log('🔗 提取的授权码:', auth_code)

      if (auth_code) {
        // 🔄 防重复发送：检查是否最近发送过相同的code
        const now = Date.now()
        if (lastSentSSOCode === auth_code && (now - lastSentTime) < 5000) {
          console.log('🔗 最近已发送过相同的授权码，跳过重复发送:', auth_code)
          return
        }

        // 如果应用已经运行，发送到主窗口
        if (appManager.mainWindow && !appManager.mainWindow.isDestroyed()) {
          console.log('🔗 向主窗口发送授权码')
          appManager.mainWindow.webContents.send('sso-callback', { code: auth_code })
          appManager.mainWindow.show()
          appManager.mainWindow.focus()

          // 更新发送记录
          lastSentSSOCode = auth_code
          lastSentTime = now
        } else {
          // 如果应用还未初始化，等待窗口创建
          setTimeout(() => {
            if (appManager.mainWindow && !appManager.mainWindow.isDestroyed()) {
              // 🔄 再次检查防重复发送
              const checkNow = Date.now()
              if (lastSentSSOCode === auth_code && (checkNow - lastSentTime) < 5000) {
                console.log('🔗 延迟发送时发现重复授权码，跳过:', auth_code)
                return
              }

              console.log('🔗 延迟向主窗口发送授权码')
              appManager.mainWindow.webContents.send('sso-callback', { code: auth_code })
              appManager.mainWindow.show()
              appManager.mainWindow.focus()

              // 更新发送记录
              lastSentSSOCode = auth_code
              lastSentTime = checkNow
            }
          }, 2000)
        }
      }
    } catch (error) {
      console.error('🔗 处理macOS协议链接失败:', error)
    }
  }
})

app.on('before-quit', async () => {
  // 标记应用正在退出
  app.isQuitting = true

  // 清理邮件服务
  if (appManager.emailService) {
    try {
      appManager.emailService.cleanup()
      console.log('Email Service cleaned up successfully')
    } catch (error) {
      console.error('Failed to cleanup Email Service:', error)
    }
  }

  // 清理Outlook日历服务
  if (appManager.outlookCalendarService) {
    try {
      appManager.outlookCalendarService.cleanup()
      console.log('Outlook Calendar Service cleaned up successfully')
    } catch (error) {
      console.error('Failed to cleanup Outlook Calendar Service:', error)
    }
  }

  // 新的 Sherpa-ONNX 集成方式：使用 Web Audio API 和 WASM，无需主进程清理
  console.log('新的 Sherpa-ONNX 集成方式：使用 Web Audio API 和 WASM，无需主进程清理')

  // 清理MCP连接
  if (appManager.mcpManager) {
    try {
      await appManager.mcpManager.cleanup()
      console.log('MCP Manager cleaned up successfully')
    } catch (error) {
      console.error('Failed to cleanup MCP Manager:', error)
    }
  }

  // 清理知识库数据库连接
  try {
    const { libsqlClient } = require('./knowledge/db')
    if (libsqlClient && typeof libsqlClient.close === 'function') {
      await libsqlClient.close()
      console.log('Knowledge database connection closed successfully')
    }
  } catch (error) {
    console.error('Failed to close knowledge database connection:', error)
  }

  // 清理托盘
  if (appManager.tray) {
    appManager.tray.destroy()
    appManager.tray = null
  }

  // 保存应用状态
  store.set('lastClosedAt', Date.now())
  console.log('App is quitting...')
  
  // 强制退出所有进程
  setTimeout(() => {
    console.log('🔄 强制退出进程')
    process.exit(0)
  }, 2000)
})

// 导出函数供其他模块使用
module.exports = {
  createMainApiClient,
  getCurrentUserToken
}