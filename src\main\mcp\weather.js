// 天气 MCP 模块
const fs = require('fs')
const { join } = require('path')
const { spawn } = require('child_process')
const Store = require('electron-store')

let initialized = false
let mcpClient = null
let mcpTransport = null
let availableTools = []

// 检查是否为开发环境
const isDev = process.env.NODE_ENV === 'development' || !require('electron').app.isPackaged

async function initialize(mcpManager) {
  if (initialized) return

  try {
    console.log('🌤️ 正在初始化天气MCP服务器...')

    // 使用主项目中的天气MCP服务器
    const weatherScriptPath = isDev
      ? join(process.cwd(), 'mcp-servers', 'weather-server', 'src', 'main.py')
      : join(process.resourcesPath, 'mcp-servers', 'weather-server', 'src', 'main.py')

    console.log('🌤️ 天气MCP脚本路径:', weatherScriptPath)
    console.log('🌤️ 当前工作目录:', process.cwd())
    console.log('🌤️ 开发环境:', isDev)

    // 验证天气MCP脚本是否存在
    if (!fs.existsSync(weatherScriptPath)) {
      console.error('❌ 天气MCP脚本不存在:', weatherScriptPath)

      // 尝试列出相关目录的内容进行调试
      const weatherDir = require('path').dirname(weatherScriptPath)
      console.log('🔍 天气MCP目录:', weatherDir)
      if (fs.existsSync(weatherDir)) {
        console.log('🔍 天气MCP目录内容:', fs.readdirSync(weatherDir))
      } else {
        console.log('🔍 天气MCP目录不存在')
        const parentDir = require('path').dirname(weatherDir)
        if (fs.existsSync(parentDir)) {
          console.log('🔍 父目录内容:', fs.readdirSync(parentDir))
        }
      }

      throw new Error(`天气MCP脚本不存在: ${weatherScriptPath}`)
    } else {
      console.log('✅ 天气MCP脚本文件存在')
    }

    // 加载MCP SDK
    console.log('📦 加载MCP SDK...')
    try {
      // 使用动态导入方式加载MCP SDK
      const stdioModule = await import('@modelcontextprotocol/sdk/client/stdio.js')
      const clientModule = await import('@modelcontextprotocol/sdk/client/index.js')
      const { StdioClientTransport } = stdioModule
      const { Client } = clientModule

      console.log('✅ MCP SDK加载成功')

      // 使用内置Python运行天气MCP服务器
      const pythonCommand = isDev
        ? (process.platform === 'win32'
          ? join(process.cwd(), 'python', 'py', 'python', 'python.exe')
          : 'python3')
        : (process.platform === 'win32'
          ? join(process.resourcesPath, 'python', 'py', 'python', 'python.exe')
          : 'python3')
      const weatherArgs = [weatherScriptPath]

      console.log('🚀 启动天气MCP服务器:')
      console.log('  - 命令:', pythonCommand)
      console.log('  - 参数:', weatherArgs)
      console.log('  - 环境:', isDev ? '开发环境' : '生产环境')
      console.log('  - 平台:', process.platform)

      // 检查Python命令是否可用
      try {
        const { execSync } = require('child_process')
        const pythonVersion = execSync(`${pythonCommand} --version`, { encoding: 'utf8', timeout: 5000 })
        console.log('🐍 Python版本:', pythonVersion.trim())
      } catch (pythonError) {
        console.warn('⚠️ Python检查失败:', pythonError.message)
        console.warn('⚠️ 这可能会导致天气MCP启动失败')
      }

      // 获取用户认证Token（使用统一的token获取函数）
      const { getCurrentUserToken } = require('../main.js')
      const userAuthToken = await getCurrentUserToken()
      console.log('🔑 获取用户认证Token:', userAuthToken ? `${userAuthToken.substring(0, 20)}...` : '未找到')

      // 创建天气MCP客户端连接
      mcpTransport = new StdioClientTransport({
        command: pythonCommand,
        args: weatherArgs,
        env: {
          ...process.env,
          // 添加Python路径以确保能找到依赖
          PYTHONPATH: isDev
            ? `${join(process.cwd(), 'mcp-servers', 'weather-server', 'src')};${join(process.cwd(), 'python', 'py', 'python', 'Lib', 'site-packages')}`
            : `${join(process.resourcesPath, 'mcp-servers', 'weather-server', 'src')};${join(process.resourcesPath, 'python', 'py', 'python', 'Lib', 'site-packages')}`,
          // 传递用户认证Token给天气MCP服务器
          USER_AUTH_TOKEN: userAuthToken
        }
      })

      // 创建客户端
      mcpClient = new Client({
        name: 'weather-client',
        version: '1.0.0'
      }, {
        capabilities: {}
      })

      // 连接到服务器
      console.log('🔗 连接到天气MCP服务器...')
      await mcpClient.connect(mcpTransport)
      console.log('✅ 已连接到天气MCP服务器')

      // 获取可用工具列表
      const toolsResponse = await mcpClient.listTools()
      availableTools = toolsResponse.tools || []
      console.log('🛠️ 天气MCP可用工具:', availableTools.map(t => t.name))

      // 注册到管理器
      mcpManager.clients.set('weather', {
        mcpClient,
        transport: mcpTransport,
        isConnected: true,
        isRealMCP: true,
        configSource: 'internal',
        availableTools
      })

      initialized = true
      console.log('✅ 天气MCP服务器初始化完成')

    } catch (sdkError) {
      console.error('❌ MCP SDK加载失败:', sdkError)
      throw sdkError
    }

  } catch (error) {
    console.error('❌ 天气MCP服务器初始化失败:', error)
    throw error
  }
}

function getConnectionStatus() {
  return {
    connected: initialized && mcpClient !== null,
    service: 'weather',
    tools: availableTools.length
  }
}

function getAvailableTools() {
  return availableTools.map(tool => ({
    name: tool.name,
    description: tool.description,
    service: 'weather'
  }))
}

async function callTool(toolName, args) {
  if (!initialized || !mcpClient) {
    throw new Error('天气MCP服务未初始化')
  }

  try {
    console.log(`🌤️ 调用天气工具: ${toolName}`)
    console.log(`🌤️ 工具参数:`, args)

    const result = await mcpClient.callTool({
      name: toolName,
      arguments: args
    })

    console.log(`🌤️ 工具调用结果:`, result)
    return {
      success: true,
      ...result.content[0]
    }
  } catch (error) {
    console.error(`❌ 天气工具调用失败: ${toolName}`, error)
    return {
      success: false,
      error: error.message
    }
  }
}

async function cleanup() {
  console.log('🧹 清理天气MCP资源...')

  if (mcpClient) {
    try {
      await mcpClient.close()
    } catch (error) {
      console.error('清理天气MCP客户端失败:', error)
    }
    mcpClient = null
  }

  if (mcpTransport) {
    try {
      await mcpTransport.close()
    } catch (error) {
      console.error('清理天气MCP传输层失败:', error)
    }
    mcpTransport = null
  }

  availableTools = []
  initialized = false
  console.log('✅ 天气MCP资源清理完成')
}

module.exports = {
  initialize,
  getConnectionStatus,
  getAvailableTools,
  callTool,
  cleanup
}