const fs = require('fs')
const { join } = require('path')
const { app } = require('electron')

// 导入各个 MCP 服务模块
const filesystemMCP = require('./filesystem')
const systemMCP = require('./system')
const wordMCP = require('./word')
const emailMCP = require('./email')
const weatherMCP = require('./weather')
const browserMCP = require('./browser')
const calendarMCP = require('./calendar')

class MCPClientManager {
  constructor() {
    this.clients = new Map()
    this.servers = new Map()
    this.initialized = false
    this.wordMCPProcess = null
    // 用户配置
    this.userConfig = null
  }

  // 获取用户配置
  getUserConfig() {
    if (!this.userConfig) {
      try {
        // 尝试从存储中读取配置
        const configPath = join(app.getPath('userData'), 'config.json')
        if (fs.existsSync(configPath)) {
          const configData = fs.readFileSync(configPath, 'utf8')
          this.userConfig = JSON.parse(configData)
          console.log('📋 已从文件加载用户配置')
        } else {
          console.log('📋 用户配置文件不存在，使用默认配置')
          this.userConfig = {}
        }
      } catch (error) {
        console.error('❌ 读取用户配置失败:', error)
        this.userConfig = {}
      }
    }
    return this.userConfig
  }

  // 保存用户配置
  saveUserConfig(config) {
    try {
      this.userConfig = config
      const configPath = join(app.getPath('userData'), 'config.json')
      fs.writeFileSync(configPath, JSON.stringify(config, null, 2), 'utf8')
      console.log('📋 用户配置已保存到文件')
      return true
    } catch (error) {
      console.error('❌ 保存用户配置失败:', error)
      return false
    }
  }

  async initialize() {
    if (this.initialized) return

    try {
      console.log('🚀 ====== 开始初始化MCP服务器 ======')
      console.log('🚀 当前时间:', new Date().toISOString())

      // 发送初始化开始状态
      this.sendStatusUpdate('开始初始化MCP服务...')

      // 初始化文件系统MCP服务器
      console.log('🚀 [1/7] 初始化文件系统MCP服务器...')
      this.sendStatusUpdate('正在初始化文件系统服务...')

      try {
        await filesystemMCP.initialize(this)
        console.log('✅ [1/7] 文件系统MCP服务器初始化完成')
        this.sendStatusUpdate('文件系统服务初始化完成')
      } catch (error) {
        console.error('❌ [1/7] 文件系统MCP服务器初始化失败:', error.message)
        this.sendStatusUpdate('文件系统服务初始化失败，将使用有限功能继续')
        // 继续执行，不要中断整体流程
      }

      // 初始化系统操作MCP（模拟）
      console.log('🚀 [2/7] 初始化系统操作MCP服务器...')
      this.sendStatusUpdate('正在初始化系统操作服务...')

      try {
        await systemMCP.initialize(this)
        console.log('✅ [2/7] 系统操作MCP服务器初始化完成')
        this.sendStatusUpdate('系统操作服务初始化完成')
      } catch (error) {
        console.error('❌ [2/7] 系统操作MCP服务器初始化失败:', error.message)
        this.sendStatusUpdate('系统操作服务初始化失败，将使用有限功能继续')
        // 继续执行，不要中断整体流程
      }

      // Word MCP服务器设置为按需加载
      this.sendStatusUpdate('配置Word文档服务为按需加载模式')
      console.log('🚀 Word MCP设置为按需加载模式')
      console.log('💡 Word功能将在首次使用时自动初始化')

      // 初始化浏览器MCP服务器
      console.log('🚀 [3/5] 初始化浏览器MCP服务器...')
      this.sendStatusUpdate('正在初始化浏览器控制服务...')

      try {
        await browserMCP.initialize(this)
        console.log('✅ [3/5] 浏览器MCP服务器初始化完成')
        this.sendStatusUpdate('浏览器控制服务初始化完成')
      } catch (error) {
        console.error('❌ [3/5] 浏览器MCP服务器初始化失败:', error.message)
        this.sendStatusUpdate('浏览器控制服务初始化失败，将使用有限功能继续')
        // 继续执行，不要中断整体流程
      }

      // 检查是否有邮件配置，决定是否初始化Email MCP服务器
      const Store = require('electron-store')
      const store = new Store()
      const emailConfig = store.get('emailConfig')
      
      if (emailConfig && emailConfig.user && emailConfig.pass) {
        console.log('🚀 [4/5] 初始化邮件MCP服务器...')
        this.sendStatusUpdate('正在初始化邮件服务...')

        try {
          await emailMCP.initialize(this)
          console.log('✅ [4/5] 邮件MCP服务器初始化完成')
          this.sendStatusUpdate('邮件服务初始化完成')
        } catch (error) {
          console.error('❌ [4/5] 邮件MCP服务器初始化失败:', error.message)
          this.sendStatusUpdate('邮件服务初始化失败，将使用有限功能继续')
          // 继续执行，不要中断整体流程
        }
      } else {
        console.log('⏭️ [4/5] 跳过邮件MCP服务器初始化（未配置或已禁用）')
        this.sendStatusUpdate('跳过邮件服务初始化')
      }

      // 初始化天气MCP服务器
      console.log('🚀 [5/5] 初始化天气MCP服务器...')
      this.sendStatusUpdate('正在初始化天气服务...')

      try {
        await weatherMCP.initialize(this)
        console.log('✅ [5/5] 天气MCP服务器初始化完成')
        this.sendStatusUpdate('天气服务初始化完成')
      } catch (error) {
        console.error('❌ [5/5] 天气MCP服务器初始化失败:', error.message)
        this.sendStatusUpdate('天气服务初始化失败，将使用有限功能继续')
        // 继续执行，不要中断整体流程
      }

      // Outlook日历MCP设置为按需加载
      this.sendStatusUpdate('配置Outlook日历服务为按需加载模式')
      console.log('🚀 Outlook日历MCP设置为按需加载模式')
      console.log('💡 日历功能将在首次使用时自动初始化')

      this.initialized = true
      console.log('🎉 ====== MCP服务器初始化完成 ======')
      this.sendStatusUpdate('MCP服务初始化完成')

    } catch (error) {
      console.error('❌ MCP服务器初始化过程中发生错误:', error)
      this.sendStatusUpdate('MCP服务初始化过程中发生错误')
      throw error
    }
  }

  // 发送状态更新
  sendStatusUpdate(message) {
    // 这里可以通过IPC发送状态更新到渲染进程
    console.log(`📡 状态更新: ${message}`)
  }

  // 获取连接状态
  getConnectionStatus() {
    const status = {}
    
    // 检查各个服务的连接状态
    status.filesystem = filesystemMCP.getConnectionStatus()
    status.system = systemMCP.getConnectionStatus()
    status.word = wordMCP.getConnectionStatus()
    status.browser = browserMCP.getConnectionStatus()
    status.email = emailMCP.getConnectionStatus()
    status.weather = weatherMCP.getConnectionStatus()
    status.calendar = calendarMCP.getConnectionStatus()

    return status
  }

  // 列出可用工具
  async listAvailableTools() {
    const tools = []
    
    // 收集各个服务的可用工具
    tools.push(...filesystemMCP.getAvailableTools())
    tools.push(...systemMCP.getAvailableTools())
    tools.push(...wordMCP.getAvailableTools())
    tools.push(...browserMCP.getAvailableTools())
    tools.push(...emailMCP.getAvailableTools())
    tools.push(...weatherMCP.getAvailableTools())
    tools.push(...calendarMCP.getAvailableTools())

    return tools
  }

  // 调用真实的MCP工具
  async callRealMCPTool(toolName, args) {
    try {
      console.log(`🚀 [REAL_MCP] 调用工具: ${toolName}`)
      console.log(`🚀 [REAL_MCP] 原始参数:`, JSON.stringify(args, null, 2))

      // 处理参数，使其适合MCP工具
      let processedArgs = { ...args }

      // 特殊参数处理
      if (toolName === 'send_email') {
        // Email工具参数处理
        console.log(`📧 [REAL_MCP] 处理邮件发送参数`)

        // 确保to参数是数组
        if (processedArgs.to && !Array.isArray(processedArgs.to)) {
          processedArgs.to = [processedArgs.to]
        }

        // 确保cc参数是数组（如果存在）
        if (processedArgs.cc && !Array.isArray(processedArgs.cc)) {
          processedArgs.cc = [processedArgs.cc]
        }

        console.log(`📧 [REAL_MCP] 邮件参数处理完成:`, processedArgs)
      } else if (toolName === 'list_email') {
        // 查询邮件工具参数处理
        console.log(`📧 [REAL_MCP] 处理邮件查询参数`)

        // 如果没有指定时间范围，默认查询最近7天
        if (!processedArgs.start_time && !processedArgs.end_time) {
          const now = new Date()
          const sevenDaysAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
          processedArgs.start_time = sevenDaysAgo.toISOString().slice(0, 19).replace('T', ' ')
          processedArgs.end_time = now.toISOString().slice(0, 19).replace('T', ' ')
        }

        console.log(`📧 [REAL_MCP] 查询参数处理完成:`, processedArgs)
      } else if (toolName === 'mark_email_as_read') {
        // 标记邮件已读工具参数处理
        console.log(`📧 [REAL_MCP] 处理邮件标记参数`)

        // 确保uid_list参数是数组
        if (processedArgs.uid_list && !Array.isArray(processedArgs.uid_list)) {
          processedArgs.uid_list = [processedArgs.uid_list]
        }

        console.log(`📧 [REAL_MCP] 标记参数处理完成:`, processedArgs)
      } else if (toolName === 'create_event') {
        // MS365 日历事件创建参数处理
        console.log(`📅 [REAL_MCP] 处理日历事件创建参数`)

        // 确保参数格式正确
        if (!processedArgs.subject) {
          console.error('❌ 缺少必需的 subject 参数')
          return {
            success: false,
            error: '缺少必需的 subject 参数',
            isRealMCP: false
          }
        }

        // 转换 ISO 格式的时间参数为外部 MCP 包期望的格式
        if (processedArgs.start) {
          const startDate = new Date(processedArgs.start)
          processedArgs.startDate = this.formatDateForOutlookMCP(startDate)
          processedArgs.startTime = this.formatTimeForOutlookMCP(startDate)
          delete processedArgs.start
        }

        if (processedArgs.end) {
          const endDate = new Date(processedArgs.end)
          processedArgs.endDate = this.formatDateForOutlookMCP(endDate)
          processedArgs.endTime = this.formatTimeForOutlookMCP(endDate)
          delete processedArgs.end
        }

        // 处理其他可能的时间参数
        if (processedArgs.startTime && !processedArgs.startDate) {
          const startDate = new Date(processedArgs.startTime)
          processedArgs.startDate = this.formatDateForOutlookMCP(startDate)
          processedArgs.startTime = this.formatTimeForOutlookMCP(startDate)
        }

        if (processedArgs.endTime && !processedArgs.endDate) {
          const endDate = new Date(processedArgs.endTime)
          processedArgs.endDate = this.formatDateForOutlookMCP(endDate)
          processedArgs.endTime = this.formatTimeForOutlookMCP(endDate)
        }

        // 清理文本参数中的特殊字符，避免命令行解析错误
        if (processedArgs.body) {
          processedArgs.body = this.sanitizeTextForVBScript(processedArgs.body)
        }
        if (processedArgs.subject) {
          processedArgs.subject = this.sanitizeTextForVBScript(processedArgs.subject)
        }
        if (processedArgs.location) {
          processedArgs.location = this.sanitizeTextForVBScript(processedArgs.location)
        }

        console.log(`📅 [REAL_MCP] 日历事件参数处理完成:`, processedArgs)
      }

      console.log(`🚀 [REAL_MCP] 处理后参数:`, JSON.stringify(processedArgs, null, 2))

      // 根据工具名称分发到对应的服务
      let result
      if (toolName.startsWith('filesystem_') || ['search_files', 'list_directory', 'read_file', 'open_file'].includes(toolName)) {
        // 文件系统工具：支持 filesystem_* 前缀和常用别名
        result = await filesystemMCP.callTool(toolName, processedArgs)
      } else if (toolName.startsWith('system_')) {
        result = await systemMCP.callTool(toolName, processedArgs)
      } else if (toolName.startsWith('word_')) {
        result = await wordMCP.callTool(toolName, processedArgs)
      } else if (toolName.startsWith('browser_')) {
        result = await browserMCP.callTool(toolName, processedArgs)
      } else if (toolName.startsWith('email_') || ['send_email', 'list_email', 'mark_email_as_read'].includes(toolName)) {
        result = await emailMCP.callTool(toolName, processedArgs)
      } else if (toolName.startsWith('weather_') || ['get_weather_forecast'].includes(toolName)) {
        result = await weatherMCP.callTool(toolName, processedArgs)
      } else if (toolName.startsWith('calendar_') || ['create_event', 'list_events', 'get_calendars'].includes(toolName)) {
        // 日历工具支持按需初始化
        if (!this.clients.has('outlook-calendar') || !this.clients.get('outlook-calendar').isConnected) {
          console.log('📅 [按需加载] Outlook日历客户端未初始化，正在初始化...')
          try {
            await this.initializeOutlookCalendarMCP()
            console.log('✅ [按需加载] Outlook日历客户端初始化成功')
          } catch (initError) {
            console.error('❌ [按需加载] Outlook日历客户端初始化失败:', initError)
            return {
              success: false,
              error: `Outlook日历MCP按需初始化失败: ${initError.message}`,
              isRealMCP: false,
              needsInitialization: true,
              suggestion: '请确保Microsoft Outlook已安装并正在运行，然后重试'
            }
          }
        }
        result = await calendarMCP.callTool(toolName, processedArgs)
      } else {
        throw new Error(`未知的工具: ${toolName}`)
      }

      console.log(`🎯 [REAL_MCP] 工具"${toolName}"执行完成，返回结果:`, JSON.stringify(result, null, 2))
      return result

    } catch (error) {
      console.error(`❌ [REAL_MCP] 调用失败:`, error)
      return {
        success: false,
        error: `MCP工具调用失败: ${error.message}`,
        toolName: toolName,
        args: args,
        exception: error.name
      }
    }
  }

  // 清理资源
  async cleanup() {
    console.log('🧹 开始清理MCP资源...')
    
    try {
      await filesystemMCP.cleanup()
      await systemMCP.cleanup()
      await wordMCP.cleanup()
      await browserMCP.cleanup()
      await emailMCP.cleanup()
      await weatherMCP.cleanup()
      await calendarMCP.cleanup()
      
      console.log('✅ MCP资源清理完成')
    } catch (error) {
      console.error('❌ MCP资源清理失败:', error)
    }
  }

  // 兼容性方法 - 文件系统操作
  async executeFileSearch(query, directory = null) {
    return await filesystemMCP.executeFileSearch(query, directory)
  }

  async listDirectory(dirPath) {
    return await filesystemMCP.listDirectory(dirPath)
  }

  async readFile(filePath) {
    return await filesystemMCP.readFile(filePath)
  }

  async openFile(filePath, options = {}) {
    return await filesystemMCP.openFile(filePath, options)
  }

  isKnowledgeReferenceFile(filePath) {
    return filesystemMCP.isKnowledgeReferenceFile(filePath)
  }

  // 兼容性方法 - 邮件服务
  async initializeEmailMCP() {
    console.log('📧 初始化邮件 MCP 服务...')
    await emailMCP.initialize(this)
    this.clients.set('email-server', { initialized: true })
    console.log('✅ 邮件 MCP 服务初始化完成')
    return this.clients.get('email-server')
  }

  // 重启邮件MCP服务
  async restartEmailMCP() {
    console.log('📧 重启邮件 MCP 服务...')
    try {
      // 先清理现有的邮件服务
      if (this.clients.has('email-server')) {
        this.clients.delete('email-server')
      }

      // 重置邮件MCP的初始化状态，强制重新初始化
      emailMCP.resetInitialization()

      // 重新初始化邮件服务
      await emailMCP.initialize(this)
      this.clients.set('email-server', { initialized: true })
      console.log('✅ 邮件 MCP 服务重启完成')
      return this.clients.get('email-server')
    } catch (error) {
      console.error('❌ 重启邮件 MCP 服务失败:', error)
      throw error
    }
  }

  // 兼容性方法 - 日历服务
  async initializeOutlookCalendarMCP() {
    console.log('📅 初始化 Outlook 日历 MCP 服务...')
    await calendarMCP.initialize(this)
    // 不要覆盖calendar.js中设置的完整客户端信息
    // this.clients.set('outlook-calendar', { initialized: true })
    console.log('✅ Outlook 日历 MCP 服务初始化完成')
    return this.clients.get('outlook-calendar')
  }

  // 兼容性方法 - 文件系统服务
  async initializeFilesystemMCP() {
    console.log('📁 初始化文件系统 MCP 服务...')
    await filesystemMCP.initialize(this)
    this.clients.set('filesystem', { initialized: true })
    console.log('✅ 文件系统 MCP 服务初始化完成')
    return this.clients.get('filesystem')
  }

  // 兼容性方法 - 浏览器服务
  async restartBrowserMCP() {
    console.log('🌐 重启浏览器 MCP 服务...')
    await browserMCP.cleanup()
    await browserMCP.initialize(this)
    this.clients.set('browser', { initialized: true })
    console.log('✅ 浏览器 MCP 服务重启完成')
    return { success: true, message: '浏览器服务重启成功' }
  }

  // 兼容性方法 - 日历服务重启
  async restartOutlookCalendarMCP() {
    console.log('📅 重启 Outlook 日历 MCP 服务...')
    await calendarMCP.cleanup()
    await calendarMCP.initialize(this)
    this.clients.set('outlook-calendar', { initialized: true })
    console.log('✅ Outlook 日历 MCP 服务重启完成')
    return { success: true, message: '日历服务重启成功' }
  }

  // 格式化日期为外部 Outlook MCP 包期望的格式 (MM/DD/YYYY)
  formatDateForOutlookMCP(date) {
    const month = (date.getMonth() + 1).toString().padStart(2, '0')
    const day = date.getDate().toString().padStart(2, '0')
    const year = date.getFullYear()
    return `${month}/${day}/${year}`
  }

  // 格式化时间为外部 Outlook MCP 包期望的格式 (HH:MM AM/PM)
  formatTimeForOutlookMCP(date) {
    let hours = date.getHours()
    const minutes = date.getMinutes().toString().padStart(2, '0')
    const ampm = hours >= 12 ? 'PM' : 'AM'

    if (hours === 0) {
      hours = 12
    } else if (hours > 12) {
      hours = hours - 12
    }

    return `${hours}:${minutes} ${ampm}`
  }

  // 清理文本中的特殊字符，避免 VBScript 命令行解析错误
  sanitizeTextForVBScript(text) {
    if (!text) return text

    return text
      .replace(/\r\n/g, ' ')  // 替换 Windows 换行符
      .replace(/\n/g, ' ')    // 替换 Unix 换行符
      .replace(/\r/g, ' ')    // 替换 Mac 换行符
      .replace(/\t/g, ' ')    // 替换制表符
      .replace(/"/g, "'")     // 替换双引号为单引号
      .replace(/\\/g, '/')    // 替换反斜杠为正斜杠
      .replace(/\s+/g, ' ')   // 合并多个空格为单个空格
      .trim()                 // 去除首尾空格
  }
}

module.exports = MCPClientManager 