// VERIFICATION_MARK: 2025-08-04T03:08:48.262Z
// Outlook日历 MCP 模块
const fs = require('fs')
const { join } = require('path')
const { app } = require('electron')
const { exec } = require('child_process')

let initialized = false
let mcpClient = null
let mcpTransport = null
let availableTools = []
let useBuiltinImplementation = false

// 检查是否为开发环境
let isDev
try {
  isDev = process.env.NODE_ENV === 'development' || !app.isPackaged
} catch (error) {
  // 在非 Electron 环境中运行时的回退
  isDev = process.env.NODE_ENV === 'development' || !global.app?.isPackaged
}

async function initialize(mcpManager) {
  if (initialized) return

  try {
    console.log('📅 正在初始化Outlook日历MCP服务器...')

    // 检查Windows环境
    if (process.platform !== 'win32') {
      console.warn('⚠️ Outlook日历MCP仅支持Windows系统')
      throw new Error('Outlook日历MCP仅支持Windows系统')
    }

    // 检查Outlook是否正在运行
    const isOutlookRunning = await new Promise((resolve) => {
      exec('tasklist | findstr OUTLOOK', (error, stdout) => {
        resolve(stdout && stdout.includes('OUTLOOK.EXE'))
      })
    })

    if (!isOutlookRunning) {
      console.warn('⚠️ Microsoft Outlook未运行')
      console.warn('💡 建议先启动Outlook并登录您的账户')
      console.warn('💡 某些日历功能可能无法正常工作')
    } else {
      console.log('✅ Microsoft Outlook正在运行')
    }

    // 首先尝试外部MCP包
    try {
      await initializeExternalMCP(mcpManager)
      console.log('✅ 使用外部outlook-calendar-mcp包')
      useBuiltinImplementation = false
    } catch (externalError) {
      console.warn('⚠️ 外部outlook-calendar-mcp包初始化失败:', externalError.message)
      console.log('🔄 回退到内置Outlook日历实现...')

      await initializeBuiltinImplementation(mcpManager, isOutlookRunning)
      console.log('✅ 使用内置Outlook日历实现')
      useBuiltinImplementation = true
    }

    initialized = true
    console.log('✅ Outlook日历MCP服务器初始化完成')

  } catch (error) {
    console.error('❌ Outlook日历MCP服务器初始化失败:', error)
    throw error
  }
}

/**
 * 尝试初始化外部MCP包
 */
async function initializeExternalMCP(mcpManager) {
  // 读取MCP配置文件
  const mcpConfigPath = isDev
    ? join(process.cwd(), 'mcp-config.json')
    : join(process.resourcesPath, 'mcp-config.json')
  console.log('📋 读取Outlook日历MCP配置文件:', mcpConfigPath)

  if (!fs.existsSync(mcpConfigPath)) {
    throw new Error('MCP配置文件不存在')
  }

  const mcpConfig = JSON.parse(fs.readFileSync(mcpConfigPath, 'utf8'))
  console.log('📋 Outlook日历MCP配置内容:', JSON.stringify(mcpConfig, null, 2))

  // 获取outlook-calendar-mcp配置
  const calendarServerConfig = mcpConfig.mcpServers['outlook-calendar-mcp']
  if (!calendarServerConfig) {
    throw new Error('未找到outlook-calendar-mcp配置')
  }

  console.log('🔧 outlook-calendar-mcp配置:', calendarServerConfig)
  console.log('🔧 命令:', calendarServerConfig.command)
  console.log('🔧 参数:', calendarServerConfig.args)

  // 解析命令和参数的绝对路径
  const resolvedCommand = calendarServerConfig.command // node命令不需要解析路径
  const resolvedArgs = calendarServerConfig.args.map(arg => {
    if (isDev) {
      return arg
    } else {
      // 打包环境下，src/main/mcp文件被解包到app.asar.unpacked中
      if (arg.startsWith('src/main/mcp/')) {
        const unpackedPath = join(process.resourcesPath, 'app.asar.unpacked', arg)
        console.log('🔍 检查unpacked路径:', unpackedPath)
        return unpackedPath
      } else if (arg.startsWith('src/main/')) {
        // 其他src/main文件在app.asar中
        const asarPath = join(process.resourcesPath, 'app.asar', arg)
        console.log('🔍 检查asar路径:', asarPath)
        return asarPath
      } else if (arg.startsWith('src/')) {
        return join(process.resourcesPath, 'app.asar', arg)
      }
      return arg
    }
  })

  console.log('🔧 解析后的命令:', resolvedCommand)
  console.log('🔧 解析后的参数:', resolvedArgs)
  console.log('🔧 环境:', isDev ? '开发环境' : '生产环境')
  console.log('🔧 process.resourcesPath:', process.resourcesPath)

  // 加载MCP SDK
  console.log('📦 加载MCP SDK...')
  // 使用动态导入方式加载MCP SDK
  const stdioModule = await import('@modelcontextprotocol/sdk/client/stdio.js')
  const clientModule = await import('@modelcontextprotocol/sdk/client/index.js')
  const { StdioClientTransport } = stdioModule
  const { Client } = clientModule

  console.log('✅ MCP SDK加载成功')

  // 创建传输层 - 让StdioClientTransport自己处理进程启动
  console.log('🚀 创建Outlook日历MCP传输层...')
  console.log('🚀 最终命令:', resolvedCommand)
  console.log('🚀 最终参数:', resolvedArgs)

  mcpTransport = new StdioClientTransport({
    command: resolvedCommand,
    args: resolvedArgs,
    env: {
      ...process.env,
      ...calendarServerConfig.env
    }
  })

  // 创建客户端
  mcpClient = new Client({
    name: 'outlook-calendar-client',
    version: '1.0.0'
  }, {
    capabilities: {}
  })

  // 连接到服务器
  console.log('🔗 连接到Outlook日历MCP服务器...')
  await mcpClient.connect(mcpTransport)
  console.log('✅ 已连接到Outlook日历MCP服务器')

  // 获取可用工具列表
  const toolsResponse = await mcpClient.listTools()
  availableTools = toolsResponse.tools || []
  console.log('🛠️ Outlook日历MCP可用工具:', availableTools.map(t => t.name))

  // 注册到管理器
  mcpManager.clients.set('outlook-calendar', {
    mcpClient,
    transport: mcpTransport,
    process: null, // 进程由StdioClientTransport管理
    isConnected: true,
    isRealMCP: true,
    configSource: 'mcp-config.json',
    availableTools
  })
}

/**
 * 转换 create_event 参数格式以匹配 VBS 脚本期望的格式
 */
function convertCreateEventArgs(args) {
  const converted = { ...args }

  // 转换 start 参数为 startDate 和 startTime
  if (args.start) {
    const startDate = new Date(args.start)
    converted.startDate = startDate.toISOString().split('T')[0] // YYYY-MM-DD
    converted.startTime = startDate.toTimeString().split(' ')[0] // HH:MM:SS
    delete converted.start
  }

  // 转换 end 参数为 endDate 和 endTime
  if (args.end) {
    const endDate = new Date(args.end)
    converted.endDate = endDate.toISOString().split('T')[0] // YYYY-MM-DD
    converted.endTime = endDate.toTimeString().split(' ')[0] // HH:MM:SS
    delete converted.end
  }

  // 转换其他可能的参数名称
  if (args.startTime && !converted.startDate) {
    // 如果传入的是 startTime（ISO格式），转换为分离的日期和时间
    const startDate = new Date(args.startTime)
    converted.startDate = startDate.toISOString().split('T')[0]
    converted.startTime = startDate.toTimeString().split(' ')[0]
  }

  if (args.endTime && !converted.endDate) {
    // 如果传入的是 endTime（ISO格式），转换为分离的日期和时间
    const endDate = new Date(args.endTime)
    converted.endDate = endDate.toISOString().split('T')[0]
    converted.endTime = endDate.toTimeString().split(' ')[0]
  }

  // 设置默认提醒时间（如果没有指定）
  if (!converted.reminder && args.reminder) {
    converted.reminder = args.reminder
  }

  // 设置是否为会议
  if (args.isMeeting !== undefined) {
    converted.isMeeting = args.isMeeting
  }

  console.log('📅 [内置MCP] 参数转换:', {
    original: args,
    converted: converted
  })

  return converted
}

/**
 * 初始化内置Outlook日历实现
 */
async function initializeBuiltinImplementation(mcpManager, isOutlookRunning) {
  console.log('🔧 使用直接集成方式启动Outlook日历MCP')
  console.log('📦 使用内置Outlook日历实现...')

  // 确定脚本路径 - 使用内置脚本
  let scriptsPath

  const isPackaged = app?.isPackaged || global.app?.isPackaged || false

  if (isPackaged) {
    // 生产环境：优先使用unpacked目录（VBS脚本必须解压才能被cscript执行）
    scriptsPath = join(process.resourcesPath, 'app.asar.unpacked', 'src', 'renderer', 'scripts')
    console.log('📂 生产环境脚本路径 (unpacked):', scriptsPath)
  } else {
    // 开发环境：从源码src/renderer/scripts目录读取
    scriptsPath = join(__dirname, '..', '..', 'renderer', 'scripts')
    console.log('📂 开发环境脚本路径:', scriptsPath)
  }

  // 检查脚本是否存在
  let createEventScript = join(scriptsPath, 'createEvent.vbs')
  let utilsScript = join(scriptsPath, 'utils.vbs')

  if (!fs.existsSync(createEventScript)) {
    console.error('❌ 未找到createEvent.vbs脚本:', createEventScript)
    console.log('📂 尝试检查替代路径...')

    // 尝试其他可能的路径（按优先级排序）
    const altPaths = [
      // 生产环境路径优先
      join(process.resourcesPath, 'app.asar.unpacked', 'src', 'renderer', 'scripts'),
      join(process.resourcesPath, 'src', 'renderer', 'scripts'),
      // 项目根目录的scripts文件夹
      join(process.cwd(), 'scripts'),
      // 源码scripts目录
      join(process.cwd(), 'src', 'renderer', 'scripts'),
      // dist中的scripts目录
      join(process.cwd(), 'dist', 'scripts'),
      // 其他可能的路径
      join(__dirname, '..', '..', 'renderer', 'scripts'),
      join(__dirname, '..', '..', '..', 'src', 'renderer', 'scripts'),
      join(__dirname, '..', '..', '..', 'scripts'),
      join(__dirname, '..', 'scripts'),
      join(app?.getAppPath?.() || global.app?.getAppPath?.() || process.cwd(), 'scripts'),
      join(app?.getAppPath?.() || global.app?.getAppPath?.() || process.cwd(), 'src', 'renderer', 'scripts'),
      // 最后的回退选项
      join(process.cwd(), 'node_modules', 'outlook-calendar-mcp', 'scripts') // 回退到npm包路径
    ]

    let foundPath = null
    for (const altPath of altPaths) {
      const altScript = join(altPath, 'createEvent.vbs')
      console.log('🔍 检查路径:', altScript)
      if (fs.existsSync(altScript)) {
        foundPath = altPath
        scriptsPath = altPath
        // 重新设置脚本路径
        createEventScript = join(scriptsPath, 'createEvent.vbs')
        utilsScript = join(scriptsPath, 'utils.vbs')
        break
      }
    }

    if (!foundPath) {
      throw new Error('Outlook日历脚本文件缺失，请检查安装')
    }

    console.log('✅ 在替代路径找到脚本:', foundPath)
  }

  if (!fs.existsSync(utilsScript)) {
    console.error('❌ 未找到utils.vbs脚本:', utilsScript)
    throw new Error('Outlook日历工具脚本缺失')
  }

  console.log('✅ 找到Outlook VBS脚本:')
  console.log('   - 最终使用路径:', scriptsPath)
  console.log('   - createEvent.vbs:', createEventScript)
  console.log('   - utils.vbs:', utilsScript)
  console.log('   - 环境类型:', isPackaged ? '生产环境' : '开发环境')

  // 创建自定义的脚本执行器
  const executeOutlookScript = async (scriptName, params = {}) => {
    return new Promise((resolve, reject) => {
      const scriptPath = join(scriptsPath, `${scriptName}.vbs`)
      let command = `cscript //NoLogo "${scriptPath}"`

      // 添加参数
      for (const [key, value] of Object.entries(params)) {
        if (value !== undefined && value !== null && value !== '') {
          // 处理特殊字符
          const escapedValue = value.toString().replace(/"/g, '\\"')
          command += ` /${key}:"${escapedValue}"`
        }
      }

      console.log('🚀 执行Outlook脚本命令:', command)

      exec(command, (error, stdout, stderr) => {
        console.log('📋 脚本输出:', stdout)
        if (stderr) console.log('📋 脚本错误输出:', stderr)

        // 检查执行错误
        if (error && !stdout.includes('SUCCESS:')) {
          return reject(new Error(`脚本执行失败: ${error.message}`))
        }

        // 检查脚本错误
        if (stdout.includes('ERROR:')) {
          const errorMessage = stdout.substring(stdout.indexOf('ERROR:') + 6).trim()
          return reject(new Error(`脚本错误: ${errorMessage}`))
        }

        // 处理成功输出
        if (stdout.includes('SUCCESS:')) {
          try {
            const jsonStr = stdout.substring(stdout.indexOf('SUCCESS:') + 8).trim()
            const result = JSON.parse(jsonStr)
            return resolve(result)
          } catch (parseError) {
            return reject(new Error(`解析脚本输出失败: ${parseError.message}`))
          }
        }

        // 如果没有明确的成功标识，但也没有错误，则认为成功
        resolve({ success: true, message: stdout.trim() })
      })
    })
  }

  // 定义可用工具
  availableTools = [
    { name: 'create_event', description: '创建新的日历事件或会议' },
    { name: 'list_events', description: '列出指定日期范围内的日历事件' },
    { name: 'find_free_slots', description: '查找日历中的空闲时间段' },
    { name: 'get_calendars', description: '获取可用的日历列表' }
  ]

  // 创建模拟的MCP客户端
  const mockClient = {
    callTool: async ({ name, arguments: args }) => {
      console.log(`📅 [内置MCP] 调用工具: ${name}`)
      console.log(`📅 [内置MCP] 工具参数:`, args)

      try {
        switch (name) {
          case 'create_event':
            // 转换参数格式以匹配 VBS 脚本期望的格式
            const convertedArgs = convertCreateEventArgs(args)
            const result = await executeOutlookScript('createEvent', convertedArgs)
            console.log('✅ 日历事件创建成功:', result)
            return {
              content: [
                {
                  type: 'text',
                  text: JSON.stringify({ success: true, eventId: result.eventId || 'unknown', ...result })
                }
              ]
            }

          case 'list_events':
            const listResult = await executeOutlookScript('listEvents', args)
            return {
              content: [
                {
                  type: 'text',
                  text: JSON.stringify(listResult)
                }
              ]
            }

          case 'find_free_slots':
            const freeResult = await executeOutlookScript('findFreeSlots', args)
            return {
              content: [
                {
                  type: 'text',
                  text: JSON.stringify(freeResult)
                }
              ]
            }

          case 'get_calendars':
            const calendarsResult = await executeOutlookScript('getCalendars')
            return {
              content: [
                {
                  type: 'text',
                  text: JSON.stringify(calendarsResult)
                }
              ]
            }

          default:
            throw new Error(`Unknown tool: ${name}`)
        }
      } catch (error) {
        console.error(`❌ 工具调用失败: ${name}`, error)
        return {
          content: [
            {
              type: 'text',
              text: JSON.stringify({ success: false, error: error.message })
            }
          ],
          isError: true
        }
      }
    },

    // 提供与MCP客户端兼容的API
    listTools: async () => {
      return {
        tools: availableTools.map(tool => ({
          name: tool.name,
          description: tool.description
        }))
      }
    },

    // 关闭函数
    close: async () => {
      // 清理资源（如果有的话）
      console.log('📅 [内置MCP] 关闭Outlook日历服务')
    }
  }

  // 保存客户端引用
  mcpClient = mockClient
  mcpManager.clients.set('outlook-calendar', {
    name: 'outlook-calendar',
    isConnected: true,
    mcpClient: mockClient,
    availableTools: availableTools,
    isRealMCP: true, // 虽然是内置集成，但它确实能工作
    configSource: '内置集成',
    lastConnected: new Date().toISOString(),
    isDirectIntegration: true,
    outlookRunning: isOutlookRunning
  })
}

function getConnectionStatus() {
  return {
    connected: initialized && mcpClient !== null,
    service: 'outlook-calendar',
    tools: availableTools.length
  }
}

function getAvailableTools() {
  return availableTools.map(tool => ({
    name: tool.name,
    description: tool.description,
    service: 'outlook-calendar'
  }))
}

async function callTool(toolName, args) {
  if (!initialized || !mcpClient) {
    throw new Error('Outlook日历MCP服务未初始化')
  }

  try {
    console.log(`📅 调用Outlook日历工具: ${toolName}`)
    console.log(`📅 工具参数:`, args)
    console.log(`📅 使用实现类型:`, useBuiltinImplementation ? '内置实现' : '外部MCP包')

    const result = await mcpClient.callTool({
      name: toolName,
      arguments: args
    })

    console.log(`📅 工具调用结果:`, result)

    // 处理结果格式
    if (result && result.content && result.content[0]) {
      const content = result.content[0]
      if (content.type === 'text') {
        try {
          const parsedResult = JSON.parse(content.text)
          return {
            success: parsedResult.success !== false,
            ...parsedResult
          }
        } catch (parseError) {
          return {
            success: true,
            text: content.text
          }
        }
      }
      return {
        success: true,
        ...content
      }
    }

    return {
      success: true,
      result: result
    }
  } catch (error) {
    console.error(`❌ Outlook日历工具调用失败: ${toolName}`, error)
    return {
      success: false,
      error: error.message
    }
  }
}

async function cleanup() {
  console.log('🧹 清理Outlook日历MCP资源...')

  if (mcpClient) {
    try {
      await mcpClient.close()
    } catch (error) {
      console.error('清理Outlook日历MCP客户端失败:', error)
    }
    mcpClient = null
  }

  if (mcpTransport) {
    try {
      await mcpTransport.close()
    } catch (error) {
      console.error('清理Outlook日历MCP传输层失败:', error)
    }
    mcpTransport = null
  }

  availableTools = []
  initialized = false
  useBuiltinImplementation = false
  console.log('✅ Outlook日历MCP资源清理完成')
}

module.exports = {
  initialize,
  getConnectionStatus,
  getAvailableTools,
  callTool,
  cleanup
}